/* Import Inter font */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Reset and base styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #000000;
  color: #ffffff;
  line-height: 1.6;
  font-size: 14px;
}

@media (min-width: 768px) {
  body {
    font-size: 16px;
  }
}

/* App layout */
.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Header */
.app-header {
  background: #1C1C1C;
  border-bottom: 1px solid #2a2a2a;
  padding: 1rem 1.5rem;
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(10px);
  box-shadow: 0 1px 20px rgba(0, 0, 0, 0.1);
}

.header-content {
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 2rem;
}

.app-header h1 {
  font-size: 1.25rem;
  font-weight: 700;
  color: #ffffff;
  margin: 0;
  letter-spacing: -0.025em;
}

@media (min-width: 768px) {
  .app-header {
    padding: 1.5rem 2rem;
  }

  .app-header h1 {
    font-size: 1.5rem;
  }
}

.header-status {
  display: flex;
  gap: 0.75rem;
  align-items: center;
  flex-wrap: wrap;
}

.status-indicator,
.auth-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
  padding: 0.375rem 0.75rem;
  border-radius: 6px;
  font-weight: 500;
  border: 1px solid transparent;
  transition: all 0.2s ease;
}

@media (min-width: 768px) {
  .header-status {
    gap: 1rem;
  }

  .status-indicator,
  .auth-status {
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
    border-radius: 8px;
  }
}

.status-indicator.connected,
.auth-status.authenticated {
  background: rgba(115, 231, 133, 0.2);
  color: #73E785;
}

.status-indicator.disconnected,
.auth-status.not-authenticated {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.status-indicator.checking {
  background: rgba(251, 191, 36, 0.2);
  color: #fbbf24;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: currentColor;
}

/* Notifications */
.notification {
  position: fixed;
  top: 80px;
  right: 1rem;
  padding: 1rem 1.5rem;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  display: flex;
  align-items: center;
  gap: 1rem;
  max-width: 400px;
  animation: slideIn 0.3s ease-out;
}

.notification.success {
  background: rgba(115, 231, 133, 0.1);
  color: #73E785;
  border: 1px solid rgba(115, 231, 133, 0.3);
}

.notification.error {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.notification.info {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.notification-close {
  background: none;
  border: none;
  font-size: 1.25rem;
  cursor: pointer;
  color: currentColor;
  opacity: 0.7;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification-close:hover {
  opacity: 1;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Auth warning */
.auth-warning {
  background: rgba(251, 191, 36, 0.1);
  border: 1px solid rgba(251, 191, 36, 0.3);
  padding: 1rem;
  margin: 1rem;
  border-radius: 8px;
}

.warning-content {
  max-width: 1200px;
  margin: 0 auto;
  text-align: center;
}

.warning-content h3 {
  margin-bottom: 0.5rem;
  color: #fbbf24;
}

.warning-content p {
  margin-bottom: 1rem;
  color: #a0a0a0;
}

.auth-btn {
  background: linear-gradient(135deg, #73E785, #5bc96b);
  color: #141414;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  font-family: 'Inter', sans-serif;
}

.auth-btn:hover {
  background: linear-gradient(135deg, #5bc96b, #4ade80);
  transform: translateY(-1px);
}

/* Tab navigation */
.tab-nav {
  background: #1C1C1C;
  padding: 1rem 1.5rem;
  display: flex;
  justify-content: center;
  border-bottom: 1px solid #2a2a2a;
}

.tab-nav-container {
  display: flex;
  background: #2a2a2a;
  border-radius: 12px;
  padding: 4px;
  gap: 2px;
  max-width: 400px;
  width: 100%;
}

.tab-btn {
  flex: 1;
  background: transparent;
  border: none;
  padding: 0.75rem 1.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #888;
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.2s ease;
  position: relative;
  font-family: 'Inter', sans-serif;
  white-space: nowrap;
}

.tab-btn:hover:not(.active) {
  color: #ffffff;
  background: rgba(255, 255, 255, 0.05);
}

.tab-btn.active {
  color: #141414;
  background: #73E785;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(115, 231, 133, 0.3);
}

@media (min-width: 768px) {
  .tab-nav {
    padding: 1.5rem 2rem;
  }

  .tab-btn {
    padding: 1rem 2rem;
    font-size: 0.9rem;
  }

  .tab-nav-container {
    max-width: 500px;
  }
}

/* Main content */
.app-main {
  flex: 1;
  width: 100%;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
}

.tab-content {
  animation: fadeIn 0.3s ease-out;
  width: 100%;
  flex: 1;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (min-width: 768px) {
  .app-main {
    padding: 2rem;
  }
}

@media (min-width: 1024px) {
  .app-main {
    padding: 3rem 2rem;
  }
}

@media (min-width: 1200px) {
  .app-main {
    padding: 3rem;
  }
}

@media (min-width: 1400px) {
  .app-main {
    padding: 4rem 3rem;
  }
}

/* Form container */
.form-container {
  background: #1C1C1C !important;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.3);
  border: 1px solid #2a2a2a;
  width: 100%;
  margin: 0;
}

.form-container h2 {
  margin-bottom: 0.5rem;
  color: #ffffff;
  font-weight: 700;
  font-size: 1.25rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  letter-spacing: -0.025em;
}

.form-description {
  color: #a0a0a0;
  margin-bottom: 2rem;
  line-height: 1.5;
  font-size: 0.9rem;
}

@media (min-width: 768px) {
  .form-container {
    padding: 2.5rem;
    border-radius: 20px;
  }

  .form-container h2 {
    font-size: 1.5rem;
    margin-bottom: 0.75rem;
  }

  .form-description {
    font-size: 1rem;
    margin-bottom: 2.5rem;
  }
}

@media (min-width: 1024px) {
  .form-container {
    padding: 3rem;
  }
}

@media (min-width: 1200px) {
  .form-container {
    padding: 3.5rem;
  }
}

@media (min-width: 1400px) {
  .form-container {
    padding: 4rem;
  }
}

/* Bug form */
.bug-form {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.25rem;
}

@media (min-width: 768px) {
  .bug-form {
    gap: 1.5rem;
  }

  .form-row {
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .bug-form {
    gap: 2rem;
  }

  .form-row {
    gap: 2rem;
  }
}

@media (min-width: 1200px) {
  .bug-form {
    gap: 2.5rem;
  }

  .form-row {
    gap: 2.5rem;
  }
}

@media (min-width: 1400px) {
  .bug-form {
    gap: 3rem;
  }

  .form-row {
    gap: 3rem;
  }
}

.form-label {
  font-weight: 600;
  color: #ffffff;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
  letter-spacing: -0.01em;
}

.form-label .required {
  color: #ef4444;
  margin-left: 2px;
}

.form-input,
.form-textarea,
.form-select {
  padding: 0.875rem 1rem;
  border: 1.5px solid #333;
  border-radius: 10px;
  font-size: 0.9rem;
  transition: all 0.2s ease;
  background: #2a2a2a;
  color: #ffffff;
  font-family: 'Inter', sans-serif;
  font-weight: 400;
}

.form-input::placeholder,
.form-textarea::placeholder {
  color: #666;
  font-weight: 400;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  outline: none;
  border-color: #73E785;
  box-shadow: 0 0 0 3px rgba(115, 231, 133, 0.1);
  background: #333;
}

.form-input:hover:not(:focus),
.form-textarea:hover:not(:focus),
.form-select:hover:not(:focus) {
  border-color: #444;
}

@media (min-width: 768px) {
  .form-label {
    font-size: 0.9rem;
  }

  .form-input,
  .form-textarea,
  .form-select {
    padding: 1rem 1.25rem;
    font-size: 1rem;
    border-radius: 12px;
  }
}

@media (min-width: 1024px) {
  .form-label {
    font-size: 1rem;
  }

  .form-input,
  .form-textarea,
  .form-select {
    padding: 1.125rem 1.5rem;
    font-size: 1rem;
    border-radius: 14px;
  }
}

@media (min-width: 1200px) {
  .form-input,
  .form-textarea,
  .form-select {
    padding: 1.25rem 1.75rem;
    font-size: 1.05rem;
    border-radius: 16px;
  }
}

.form-textarea {
  resize: vertical;
  min-height: 100px;
  font-family: inherit;
}

.form-select {
  cursor: pointer;
}



/* File upload */
.file-drop-zone {
  border: 2px dashed #444;
  border-radius: 12px;
  padding: 2rem;
  text-align: center;
  transition: all 0.2s ease;
  background: #2a2a2a;
  position: relative;
  cursor: pointer;
}

.file-drop-zone:hover,
.file-drop-zone.drag-active {
  border-color: #73E785;
  background: rgba(115, 231, 133, 0.05);
  transform: translateY(-1px);
}

@media (min-width: 768px) {
  .file-drop-zone {
    padding: 2.5rem;
    border-radius: 16px;
  }
}

.file-drop-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  color: #888;
}

.file-drop-icon {
  font-size: 2rem;
  opacity: 0.6;
}

.file-input {
  position: absolute;
  inset: 0;
  opacity: 0;
  cursor: pointer;
}

.file-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.screenshot-preview {
  max-width: 200px;
  max-height: 200px;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.file-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.file-name {
  font-size: 0.875rem;
  color: #6c757d;
}

.remove-file-btn {
  background: #dc3545;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.remove-file-btn:hover {
  background: #c82333;
}

/* Submit button */
.submit-btn {
  background: linear-gradient(135deg, #73E785, #5bc96b);
  color: #141414;
  border: none;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-size: 0.95rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 1.5rem;
  font-family: 'Inter', sans-serif;
  width: 100%;
  letter-spacing: -0.01em;
  position: relative;
  overflow: hidden;
}

.submit-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.submit-btn:hover:not(:disabled)::before {
  left: 100%;
}

.submit-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #5bc96b, #4ade80);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(115, 231, 133, 0.4);
}

.submit-btn:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: 0 4px 12px rgba(115, 231, 133, 0.3);
}

.submit-btn:disabled {
  background: #444;
  color: #888;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.submit-btn.submitting {
  background: #444;
  color: #888;
}

@media (min-width: 768px) {
  .submit-btn {
    padding: 1.25rem 2.5rem;
    font-size: 1rem;
    border-radius: 14px;
    margin-top: 2rem;
  }
}

/* Bug list */
.bug-list {
  background: #1C1C1C;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.3);
  border: 1px solid #2a2a2a;
  width: 100%;
}

.bug-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.bug-list-header h2 {
  margin: 0;
  color: #ffffff;
  font-weight: 600;
}

.refresh-btn {
  background: #2a2a2a;
  border: 1px solid #444;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #ffffff;
  font-family: 'Inter', sans-serif;
}

.refresh-btn:hover {
  background: #333;
  border-color: #555;
}

/* Loading and error states */
.loading,
.error-message {
  text-align: center;
  padding: 3rem 1rem;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message h3 {
  color: #dc3545;
  margin-bottom: 0.5rem;
}

.error-message p {
  color: #6c757d;
  margin-bottom: 1rem;
}

.retry-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.retry-btn:hover {
  background: #0056b3;
}

/* Empty state */
.empty-state {
  text-align: center;
  padding: 3rem 1rem;
  color: #6c757d;
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.6;
}

.empty-state h3 {
  margin-bottom: 0.5rem;
  color: #495057;
}

/* Bug items */
.bug-items {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.bug-item {
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 1.5rem;
  transition: all 0.2s;
  background: #fff;
}

.bug-item:hover {
  border-color: #007bff;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
}

.bug-item.completed {
  opacity: 0.7;
  background: #f8f9fa;
}

.bug-header {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.bug-priority {
  font-size: 1.25rem;
  flex-shrink: 0;
}

.bug-title {
  flex: 1;
  font-weight: 500;
  color: #1a1a1a;
  line-height: 1.4;
}

.bug-status {
  font-size: 1.25rem;
  flex-shrink: 0;
}

.bug-description {
  color: #6c757d;
  margin-bottom: 1rem;
  line-height: 1.5;
  padding-left: 2rem;
}

.bug-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-left: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.bug-date {
  font-size: 0.875rem;
  color: #6c757d;
}

.basecamp-link {
  color: #007bff;
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
  transition: color 0.2s;
}

.basecamp-link:hover {
  color: #0056b3;
  text-decoration: underline;
}

/* Footer */
.app-footer {
  background: #1C1C1C;
  border-top: 1px solid #2a2a2a;
  padding: 1.5rem;
  text-align: center;
  margin-top: auto;
}

.app-footer p {
  margin: 0;
  color: #888;
  font-size: 0.875rem;
  font-weight: 400;
}

@media (min-width: 768px) {
  .app-footer {
    padding: 2rem;
  }
}

/* Mobile-specific responsive design */
@media (max-width: 767px) {
  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .header-status {
    width: 100%;
    justify-content: space-between;
  }

  .notification {
    top: auto;
    bottom: 1rem;
    right: 1rem;
    left: 1rem;
    max-width: none;
  }

  .tab-nav-container {
    width: 100%;
    max-width: none;
  }

  .tab-btn {
    font-size: 0.8rem;
    padding: 0.75rem 1rem;
  }

  .form-container {
    margin: 0;
    border-radius: 12px;
  }

  .file-drop-zone {
    padding: 1.5rem 1rem;
  }

  .screenshot-preview {
    max-width: 150px;
    max-height: 150px;
  }
}

/* Small mobile devices */
@media (max-width: 480px) {
  .app-header {
    padding: 1rem;
  }

  .app-header h1 {
    font-size: 1.1rem;
  }

  .tab-nav {
    padding: 1rem;
  }

  .tab-btn {
    font-size: 0.75rem;
    padding: 0.625rem 0.75rem;
  }

  .app-main {
    padding: 1rem;
  }

  .form-container {
    padding: 1.25rem;
  }

  .form-container h2 {
    font-size: 1.1rem;
  }

  .submit-btn {
    padding: 1rem 1.5rem;
    font-size: 0.9rem;
  }

  .file-drop-zone {
    padding: 1rem;
  }

  .file-drop-icon {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .app-header h1 {
    font-size: 1.1rem;
  }

  .form-container,
  .bug-list {
    padding: 1rem;
    margin: 0 0.5rem;
  }

  .app-main {
    padding: 1rem 0.5rem;
  }

  .tab-btn {
    padding: 0.75rem 0.75rem;
    font-size: 0.75rem;
  }

  .submit-btn {
    padding: 0.875rem 1.5rem;
    font-size: 0.9rem;
  }

  .bug-item {
    padding: 1rem;
  }

  .file-drop-zone {
    padding: 1rem;
  }

  .file-drop-icon {
    font-size: 1.5rem;
  }
}

/* Dark mode support (optional) */
@media (prefers-color-scheme: dark) {
  body {
    background-color: #1a1a1a;
    color: #e9ecef;
  }

  .app-header,
  .form-container,
  .bug-list,
  .bug-item {
    background: #2a2a2a;
    border-color: #4a5568;
  }

  .form-input,
  .form-textarea,
  .form-select {
    background: #2a2a2a;
    border-color: #4a5568;
    color: #e9ecef;
  }

  .file-drop-zone {
    background: #2a2a2a;
    border-color: #4a5568;
  }

  .refresh-btn {
    background: #4a5568;
    border-color: #718096;
    color: #e9ecef;
  }

  .refresh-btn:hover {
    background: #718096;
  }
}
