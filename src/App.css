/* Reset and base styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f8f9fa;
  color: #1a1a1a;
  line-height: 1.6;
}

/* App layout */
.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Header */
.app-header {
  background: #fff;
  border-bottom: 1px solid #e1e5e9;
  padding: 1rem;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.app-header h1 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
}

.header-status {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
}

.status-indicator,
.auth-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-weight: 500;
}

.status-indicator.connected,
.auth-status.authenticated {
  background: #d4edda;
  color: #155724;
}

.status-indicator.disconnected,
.auth-status.not-authenticated {
  background: #f8d7da;
  color: #721c24;
}

.status-indicator.checking {
  background: #fff3cd;
  color: #856404;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: currentColor;
}

/* Notifications */
.notification {
  position: fixed;
  top: 80px;
  right: 1rem;
  padding: 1rem 1.5rem;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  display: flex;
  align-items: center;
  gap: 1rem;
  max-width: 400px;
  animation: slideIn 0.3s ease-out;
}

.notification.success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.notification.error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.notification.info {
  background: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}

.notification-close {
  background: none;
  border: none;
  font-size: 1.25rem;
  cursor: pointer;
  color: currentColor;
  opacity: 0.7;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification-close:hover {
  opacity: 1;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Auth warning */
.auth-warning {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  padding: 1rem;
  margin: 1rem;
  border-radius: 8px;
}

.warning-content {
  max-width: 1200px;
  margin: 0 auto;
  text-align: center;
}

.warning-content h3 {
  margin-bottom: 0.5rem;
  color: #856404;
}

.warning-content p {
  margin-bottom: 1rem;
  color: #856404;
}

.auth-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.auth-btn:hover {
  background: #0056b3;
}

/* Tab navigation */
.tab-nav {
  background: #fff;
  border-bottom: 1px solid #e1e5e9;
  padding: 0 1rem;
  display: flex;
  max-width: 1200px;
  margin: 0 auto;
}

.tab-btn {
  background: none;
  border: none;
  padding: 1rem 1.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #6c757d;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
  position: relative;
}

.tab-btn:hover {
  color: #495057;
  background: #f8f9fa;
}

.tab-btn.active {
  color: #007bff;
  border-bottom-color: #007bff;
}

/* Main content */
.app-main {
  flex: 1;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  padding: 2rem 1rem;
}

.tab-content {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Form container */
.form-container {
  background: #fff;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e5e9;
}

.form-container h2 {
  margin-bottom: 0.5rem;
  color: #1a1a1a;
  font-weight: 600;
}

.form-description {
  color: #6c757d;
  margin-bottom: 2rem;
  line-height: 1.5;
}

/* Bug form */
.bug-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-label {
  font-weight: 500;
  color: #1a1a1a;
  font-size: 0.875rem;
}

.form-input,
.form-textarea,
.form-select {
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.2s, box-shadow 0.2s;
  background: #fff;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 100px;
  font-family: inherit;
}

.form-select {
  cursor: pointer;
}

/* File upload */
.file-drop-zone {
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
  transition: all 0.2s;
  background: #fafbfc;
  position: relative;
  cursor: pointer;
}

.file-drop-zone:hover,
.file-drop-zone.drag-active {
  border-color: #007bff;
  background: #f0f8ff;
}

.file-drop-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.file-drop-icon {
  font-size: 2rem;
  opacity: 0.6;
}

.file-input {
  position: absolute;
  inset: 0;
  opacity: 0;
  cursor: pointer;
}

.file-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.screenshot-preview {
  max-width: 200px;
  max-height: 200px;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.file-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.file-name {
  font-size: 0.875rem;
  color: #6c757d;
}

.remove-file-btn {
  background: #dc3545;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.remove-file-btn:hover {
  background: #c82333;
}

/* Submit button */
.submit-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 1rem;
}

.submit-btn:hover:not(:disabled) {
  background: #0056b3;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.submit-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.submit-btn.submitting {
  background: #6c757d;
}

/* Bug list */
.bug-list {
  background: #fff;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e5e9;
}

.bug-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.bug-list-header h2 {
  margin: 0;
  color: #1a1a1a;
  font-weight: 600;
}

.refresh-btn {
  background: #f8f9fa;
  border: 1px solid #d1d5db;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.refresh-btn:hover {
  background: #e9ecef;
  border-color: #adb5bd;
}

/* Loading and error states */
.loading,
.error-message {
  text-align: center;
  padding: 3rem 1rem;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message h3 {
  color: #dc3545;
  margin-bottom: 0.5rem;
}

.error-message p {
  color: #6c757d;
  margin-bottom: 1rem;
}

.retry-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.retry-btn:hover {
  background: #0056b3;
}

/* Empty state */
.empty-state {
  text-align: center;
  padding: 3rem 1rem;
  color: #6c757d;
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.6;
}

.empty-state h3 {
  margin-bottom: 0.5rem;
  color: #495057;
}

/* Bug items */
.bug-items {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.bug-item {
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 1.5rem;
  transition: all 0.2s;
  background: #fff;
}

.bug-item:hover {
  border-color: #007bff;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
}

.bug-item.completed {
  opacity: 0.7;
  background: #f8f9fa;
}

.bug-header {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.bug-priority {
  font-size: 1.25rem;
  flex-shrink: 0;
}

.bug-title {
  flex: 1;
  font-weight: 500;
  color: #1a1a1a;
  line-height: 1.4;
}

.bug-status {
  font-size: 1.25rem;
  flex-shrink: 0;
}

.bug-description {
  color: #6c757d;
  margin-bottom: 1rem;
  line-height: 1.5;
  padding-left: 2rem;
}

.bug-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-left: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.bug-date {
  font-size: 0.875rem;
  color: #6c757d;
}

.basecamp-link {
  color: #007bff;
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
  transition: color 0.2s;
}

.basecamp-link:hover {
  color: #0056b3;
  text-decoration: underline;
}

/* Footer */
.app-footer {
  background: #f8f9fa;
  border-top: 1px solid #e1e5e9;
  padding: 1rem;
  text-align: center;
  margin-top: auto;
}

.app-footer p {
  margin: 0;
  color: #6c757d;
  font-size: 0.875rem;
}

/* Responsive design */
@media (max-width: 768px) {
  .app-header {
    padding: 0.75rem;
  }

  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .app-header h1 {
    font-size: 1.25rem;
  }

  .header-status {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
    width: 100%;
  }

  .status-indicator,
  .auth-status {
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
  }

  .notification {
    top: auto;
    bottom: 1rem;
    right: 1rem;
    left: 1rem;
    max-width: none;
  }

  .tab-nav {
    padding: 0 0.75rem;
  }

  .tab-btn {
    padding: 0.75rem 1rem;
    font-size: 0.8rem;
  }

  .app-main {
    padding: 1rem 0.75rem;
  }

  .form-container,
  .bug-list {
    padding: 1.5rem;
    border-radius: 8px;
  }

  .bug-list-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .bug-header {
    flex-direction: column;
    gap: 0.5rem;
  }

  .bug-description,
  .bug-footer {
    padding-left: 0;
  }

  .bug-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .file-drop-zone {
    padding: 1.5rem;
  }

  .screenshot-preview {
    max-width: 150px;
    max-height: 150px;
  }
}

@media (max-width: 480px) {
  .app-header h1 {
    font-size: 1.1rem;
  }

  .form-container,
  .bug-list {
    padding: 1rem;
    margin: 0 0.5rem;
  }

  .app-main {
    padding: 1rem 0.5rem;
  }

  .tab-btn {
    padding: 0.75rem 0.75rem;
    font-size: 0.75rem;
  }

  .submit-btn {
    padding: 0.875rem 1.5rem;
    font-size: 0.9rem;
  }

  .bug-item {
    padding: 1rem;
  }

  .file-drop-zone {
    padding: 1rem;
  }

  .file-drop-icon {
    font-size: 1.5rem;
  }
}

/* Dark mode support (optional) */
@media (prefers-color-scheme: dark) {
  body {
    background-color: #1a1a1a;
    color: #e9ecef;
  }

  .app-header,
  .form-container,
  .bug-list,
  .bug-item {
    background: #2d3748;
    border-color: #4a5568;
  }

  .form-input,
  .form-textarea,
  .form-select {
    background: #2d3748;
    border-color: #4a5568;
    color: #e9ecef;
  }

  .file-drop-zone {
    background: #2d3748;
    border-color: #4a5568;
  }

  .refresh-btn {
    background: #4a5568;
    border-color: #718096;
    color: #e9ecef;
  }

  .refresh-btn:hover {
    background: #718096;
  }
}
