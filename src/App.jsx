import { useState, useEffect } from 'react';
import BugForm from './components/BugForm';
import BugList from './components/BugList';
import { bugAPI } from './services/api';
import './App.css';

function App() {
  const [activeTab, setActiveTab] = useState('submit');
  const [authStatus, setAuthStatus] = useState(null);
  const [notification, setNotification] = useState(null);
  const [serverStatus, setServerStatus] = useState('checking');

  useEffect(() => {
    checkServerStatus();
    checkAuthStatus();
  }, []);

  const checkServerStatus = async () => {
    try {
      await bugAPI.getHealth();
      setServerStatus('connected');
    } catch (error) {
      console.error('Server connection failed:', error);
      setServerStatus('disconnected');
    }
  };

  const checkAuthStatus = async () => {
    try {
      const status = await bugAPI.getAuthStatus();
      setAuthStatus(status);
    } catch (error) {
      console.error('Auth status check failed:', error);
      setAuthStatus({ authenticated: false });
    }
  };

  const showNotification = (message, type = 'success') => {
    setNotification({ message, type });
    setTimeout(() => setNotification(null), 5000);
  };

  const handleSubmitSuccess = (result) => {
    showNotification('Bug submitted successfully! 🎉', 'success');
    // Refresh auth status in case it changed
    checkAuthStatus();
  };

  const handleSubmitError = (error) => {
    const message = error.response?.data?.error || 'Failed to submit bug';
    showNotification(message, 'error');

    // If auth error, refresh auth status
    if (error.response?.status === 401) {
      checkAuthStatus();
    }
  };

  const handleAuthSetup = () => {
    if (authStatus?.authUrl) {
      window.open(authStatus.authUrl, '_blank');
      showNotification('Please complete authentication in the new window', 'info');
    }
  };

  return (
    <div className="app">
      <header className="app-header">
        <div className="header-content">
          <h1>Bug Tracker</h1>
          <div className="header-status">
            <div className={`status-indicator ${serverStatus}`}>
              <span className="status-dot"></span>
              {serverStatus === 'connected' ? 'Server Connected' :
               serverStatus === 'disconnected' ? 'Server Disconnected' : 'Checking...'}
            </div>
            {authStatus && (
              <div className={`auth-status ${authStatus.authenticated ? 'authenticated' : 'not-authenticated'}`}>
                <span className="status-dot"></span>
                {authStatus.authenticated ? 'Basecamp Connected' : 'Basecamp Not Connected'}
              </div>
            )}
          </div>
        </div>
      </header>

      {notification && (
        <div className={`notification ${notification.type}`}>
          <span>{notification.message}</span>
          <button onClick={() => setNotification(null)} className="notification-close">
            ×
          </button>
        </div>
      )}

      {authStatus && !authStatus.authenticated && (
        <div className="auth-warning">
          <div className="warning-content">
            <h3>⚠️ Basecamp Authentication Required</h3>
            <p>You need to authenticate with Basecamp to submit bug reports.</p>
            <button onClick={handleAuthSetup} className="auth-btn">
              Connect to Basecamp
            </button>
          </div>
        </div>
      )}

      <nav className="tab-nav">
        <div className="tab-nav-container">
          <button
            className={`tab-btn ${activeTab === 'submit' ? 'active' : ''}`}
            onClick={() => setActiveTab('submit')}
          >
            Submit Bug
          </button>
          <button
            className={`tab-btn ${activeTab === 'list' ? 'active' : ''}`}
            onClick={() => setActiveTab('list')}
          >
            View Bugs
          </button>
        </div>
      </nav>

      <main className="app-main">
        {activeTab === 'submit' ? (
          <div className="tab-content">
            <div className="form-container">
              <h2>⚠️ Report New Issue</h2>
              <p className="form-description">
                Submit a bug report or issue to be tracked in Basecamp
              </p>
              <BugForm
                onSubmitSuccess={handleSubmitSuccess}
                onSubmitError={handleSubmitError}
              />
            </div>
          </div>
        ) : (
          <div className="tab-content">
            <BugList />
          </div>
        )}
      </main>

      <footer className="app-footer">
        <p>Bug reports are automatically synced with Basecamp</p>
      </footer>
    </div>
  );
}

export default App;
