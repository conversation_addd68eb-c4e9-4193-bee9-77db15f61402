import axios from 'axios';

const API_BASE_URL = 'http://localhost:3001';

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for logging
api.interceptors.request.use(
  (config) => {
    console.log(`Making ${config.method?.toUpperCase()} request to ${config.url}`);
    return config;
  },
  (error) => {
    console.error('Request error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    console.error('API Error:', error.response?.data || error.message);
    return Promise.reject(error);
  }
);

export const bugAPI = {
  // Submit a new bug
  submitBug: async (bugData) => {
    const formData = new FormData();
    formData.append('title', bugData.title);
    formData.append('description', bugData.description);
    formData.append('priority', bugData.priority);
    formData.append('category', bugData.category || '');
    formData.append('stepsToReproduce', bugData.stepsToReproduce || '');
    formData.append('expectedBehavior', bugData.expectedBehavior || '');
    formData.append('actualBehavior', bugData.actualBehavior || '');
    formData.append('environment', bugData.environment || '');

    if (bugData.screenshot) {
      formData.append('screenshot', bugData.screenshot);
    }

    const response = await api.post('/api/bugs/submit', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response.data;
  },

  // Get list of submitted bugs
  getBugs: async () => {
    const response = await api.get('/api/bugs/list');
    return response.data;
  },

  // Get authentication status
  getAuthStatus: async () => {
    const response = await api.get('/auth/status');
    return response.data;
  },

  // Get health status
  getHealth: async () => {
    const response = await api.get('/health');
    return response.data;
  },

  // Get Basecamp projects (for debugging)
  getProjects: async () => {
    const response = await api.get('/api/bugs/projects');
    return response.data;
  }
};

export default api;
