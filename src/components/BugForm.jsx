import { useState } from 'react';
import { bugAPI } from '../services/api';

const BugForm = ({ onSubmitSuccess, onSubmitError }) => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    priority: 'Medium',
    category: '',
    stepsToReproduce: '',
    expectedBehavior: '',
    actualBehavior: '',
    environment: ''
  });
  const [screenshot, setScreenshot] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [dragActive, setDragActive] = useState(false);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    console.log('🔍 Form field changed:', { name, value });
    setFormData(prev => {
      const newData = {
        ...prev,
        [name]: value
      };
      console.log('📝 Updated form data:', newData);
      return newData;
    });
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file && file.type.startsWith('image/')) {
      setScreenshot(file);
    } else {
      alert('Please select an image file');
    }
  };

  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const file = e.dataTransfer.files[0];
      if (file.type.startsWith('image/')) {
        setScreenshot(file);
      } else {
        alert('Please drop an image file');
      }
    }
  };

  const removeScreenshot = () => {
    setScreenshot(null);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!formData.title.trim()) {
      alert('Please enter a bug title');
      return;
    }

    if (!formData.description || !formData.description.trim()) {
      alert('Please enter a description');
      return;
    }

    setIsSubmitting(true);

    console.log('🚀 Form submission - Current formData:', formData);

    try {
      const bugData = {
        title: formData.title.trim(),
        description: formData.description.trim(),
        priority: formData.priority,
        category: formData.category.trim(),
        stepsToReproduce: formData.stepsToReproduce.trim(),
        expectedBehavior: formData.expectedBehavior.trim(),
        actualBehavior: formData.actualBehavior.trim(),
        environment: formData.environment.trim(),
        screenshot: screenshot
      };

      console.log('📦 Prepared bugData for API:', bugData);

      const result = await bugAPI.submitBug(bugData);
      
      // Reset form
      setFormData({
        title: '',
        description: '',
        priority: 'Medium',
        category: '',
        stepsToReproduce: '',
        expectedBehavior: '',
        actualBehavior: '',
        environment: ''
      });
      setScreenshot(null);

      onSubmitSuccess?.(result);
    } catch (error) {
      console.error('Error submitting bug:', error);
      onSubmitError?.(error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div>
      {/* Debug info - remove this later */}
      <div style={{ background: '#333', color: '#fff', padding: '10px', marginBottom: '20px', fontSize: '12px' }}>
        <strong>🔍 Debug - Current Form State:</strong>
        <pre>{JSON.stringify(formData, null, 2)}</pre>
      </div>

    <form onSubmit={handleSubmit} className="bug-form">
      <div className="form-group">
        <label htmlFor="title" className="form-label">
          Title <span className="required">*</span>
        </label>
        <input
          type="text"
          id="title"
          name="title"
          value={formData.title}
          onChange={handleInputChange}
          placeholder="Brief description of the issue"
          className="form-input"
          required
          disabled={isSubmitting}
        />
      </div>

      <div className="form-row">
        <div className="form-group">
          <label htmlFor="priority" className="form-label">
            Priority <span className="required">*</span>
          </label>
          <select
            id="priority"
            name="priority"
            value={formData.priority}
            onChange={handleInputChange}
            className="form-select"
            required
            disabled={isSubmitting}
          >
            <option value="">Select priority</option>
            <option value="Low">🟢 Low</option>
            <option value="Medium">🟡 Medium</option>
            <option value="High">🔴 High</option>
          </select>
        </div>

        <div className="form-group">
          <label htmlFor="category" className="form-label">
            Category
          </label>
          <select
            id="category"
            name="category"
            value={formData.category}
            onChange={handleInputChange}
            className="form-select"
            disabled={isSubmitting}
          >
            <option value="">Select category</option>
            <option value="UI/UX">UI/UX</option>
            <option value="Functionality">Functionality</option>
            <option value="Performance">Performance</option>
            <option value="Security">Security</option>
            <option value="Compatibility">Compatibility</option>
            <option value="Data">Data</option>
            <option value="Other">Other</option>
          </select>
        </div>
      </div>

      <div className="form-group">
        <label htmlFor="description" className="form-label">
          Description <span className="required">*</span>
        </label>
        <textarea
          id="description"
          name="description"
          value={formData.description}
          onChange={handleInputChange}
          placeholder="Detailed description of the issue"
          className="form-textarea"
          rows={4}
          required
          disabled={isSubmitting}
        />
      </div>

      <div className="form-group">
        <label htmlFor="stepsToReproduce" className="form-label">
          Steps to Reproduce
        </label>
        <textarea
          id="stepsToReproduce"
          name="stepsToReproduce"
          value={formData.stepsToReproduce}
          onChange={handleInputChange}
          placeholder="1. Step one&#10;2. Step two&#10;3. Step three"
          className="form-textarea"
          rows={4}
          disabled={isSubmitting}
        />
      </div>

      <div className="form-row">
        <div className="form-group">
          <label htmlFor="expectedBehavior" className="form-label">
            Expected Behavior
          </label>
          <textarea
            id="expectedBehavior"
            name="expectedBehavior"
            value={formData.expectedBehavior}
            onChange={handleInputChange}
            placeholder="What should happen"
            className="form-textarea"
            rows={3}
            disabled={isSubmitting}
          />
        </div>

        <div className="form-group">
          <label htmlFor="actualBehavior" className="form-label">
            Actual Behavior
          </label>
          <textarea
            id="actualBehavior"
            name="actualBehavior"
            value={formData.actualBehavior}
            onChange={handleInputChange}
            placeholder="What actually happens"
            className="form-textarea"
            rows={3}
            disabled={isSubmitting}
          />
        </div>
      </div>

      <div className="form-group">
        <label htmlFor="environment" className="form-label">
          Environment
        </label>
        <input
          type="text"
          id="environment"
          name="environment"
          value={formData.environment}
          onChange={handleInputChange}
          placeholder="Browser, OS, device info"
          className="form-input"
          disabled={isSubmitting}
        />
      </div>

      <div className="form-group">
        <label className="form-label">
          Screenshot (Optional)
        </label>
        <div
          className={`file-drop-zone ${dragActive ? 'drag-active' : ''}`}
          onDragEnter={handleDrag}
          onDragLeave={handleDrag}
          onDragOver={handleDrag}
          onDrop={handleDrop}
        >
          {screenshot ? (
            <div className="file-preview">
              <img
                src={URL.createObjectURL(screenshot)}
                alt="Screenshot preview"
                className="screenshot-preview"
              />
              <div className="file-info">
                <span className="file-name">{screenshot.name}</span>
                <button
                  type="button"
                  onClick={removeScreenshot}
                  className="remove-file-btn"
                  disabled={isSubmitting}
                >
                  Remove
                </button>
              </div>
            </div>
          ) : (
            <div className="file-drop-content">
              <div className="file-drop-icon">📷</div>
              <p>Drop screenshot here or click to browse</p>
              <input
                type="file"
                accept="image/*"
                onChange={handleFileChange}
                className="file-input"
                disabled={isSubmitting}
              />
            </div>
          )}
        </div>
      </div>

      <button
        type="submit"
        className={`submit-btn ${isSubmitting ? 'submitting' : ''}`}
        disabled={isSubmitting}
      >
        {isSubmitting ? 'Submitting Bug Report...' : 'Submit Bug Report'}
      </button>
    </form>
    </div>
  );
};

export default BugForm;
