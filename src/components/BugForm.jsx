import { useState } from 'react';
import { bugAPI } from '../services/api';

const BugForm = ({ onSubmitSuccess, onSubmitError }) => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    priority: 'Medium'
  });
  const [screenshot, setScreenshot] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [dragActive, setDragActive] = useState(false);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file && file.type.startsWith('image/')) {
      setScreenshot(file);
    } else {
      alert('Please select an image file');
    }
  };

  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const file = e.dataTransfer.files[0];
      if (file.type.startsWith('image/')) {
        setScreenshot(file);
      } else {
        alert('Please drop an image file');
      }
    }
  };

  const removeScreenshot = () => {
    setScreenshot(null);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!formData.title.trim()) {
      alert('Please enter a bug title');
      return;
    }

    setIsSubmitting(true);

    try {
      const bugData = {
        title: formData.title.trim(),
        description: formData.description.trim(),
        priority: formData.priority,
        screenshot: screenshot
      };

      const result = await bugAPI.submitBug(bugData);
      
      // Reset form
      setFormData({
        title: '',
        description: '',
        priority: 'Medium'
      });
      setScreenshot(null);

      onSubmitSuccess?.(result);
    } catch (error) {
      console.error('Error submitting bug:', error);
      onSubmitError?.(error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="bug-form">
      <div className="form-group">
        <label htmlFor="title" className="form-label">
          Bug Title *
        </label>
        <input
          type="text"
          id="title"
          name="title"
          value={formData.title}
          onChange={handleInputChange}
          placeholder="Brief description of the bug"
          className="form-input"
          required
          disabled={isSubmitting}
        />
      </div>

      <div className="form-group">
        <label htmlFor="description" className="form-label">
          Description
        </label>
        <textarea
          id="description"
          name="description"
          value={formData.description}
          onChange={handleInputChange}
          placeholder="Detailed description of the bug, steps to reproduce, expected vs actual behavior..."
          className="form-textarea"
          rows={4}
          disabled={isSubmitting}
        />
      </div>

      <div className="form-group">
        <label htmlFor="priority" className="form-label">
          Priority *
        </label>
        <select
          id="priority"
          name="priority"
          value={formData.priority}
          onChange={handleInputChange}
          className="form-select"
          required
          disabled={isSubmitting}
        >
          <option value="Low">🟢 Low</option>
          <option value="Medium">🟡 Medium</option>
          <option value="High">🔴 High</option>
        </select>
      </div>

      <div className="form-group">
        <label className="form-label">
          Screenshot (Optional)
        </label>
        <div
          className={`file-drop-zone ${dragActive ? 'drag-active' : ''}`}
          onDragEnter={handleDrag}
          onDragLeave={handleDrag}
          onDragOver={handleDrag}
          onDrop={handleDrop}
        >
          {screenshot ? (
            <div className="file-preview">
              <img
                src={URL.createObjectURL(screenshot)}
                alt="Screenshot preview"
                className="screenshot-preview"
              />
              <div className="file-info">
                <span className="file-name">{screenshot.name}</span>
                <button
                  type="button"
                  onClick={removeScreenshot}
                  className="remove-file-btn"
                  disabled={isSubmitting}
                >
                  Remove
                </button>
              </div>
            </div>
          ) : (
            <div className="file-drop-content">
              <div className="file-drop-icon">📷</div>
              <p>Drop screenshot here or click to browse</p>
              <input
                type="file"
                accept="image/*"
                onChange={handleFileChange}
                className="file-input"
                disabled={isSubmitting}
              />
            </div>
          )}
        </div>
      </div>

      <button
        type="submit"
        className={`submit-btn ${isSubmitting ? 'submitting' : ''}`}
        disabled={isSubmitting}
      >
        {isSubmitting ? 'Submitting...' : 'Submit Bug Report'}
      </button>
    </form>
  );
};

export default BugForm;
