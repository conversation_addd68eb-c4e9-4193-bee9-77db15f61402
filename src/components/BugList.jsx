import { useState, useEffect } from 'react';
import { bugAPI } from '../services/api';

const BugList = () => {
  const [bugs, setBugs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchBugs();
  }, []);

  const fetchBugs = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await bugAPI.getBugs();
      setBugs(response.bugs || []);
    } catch (err) {
      console.error('Error fetching bugs:', err);
      setError(err.response?.data?.error || 'Failed to fetch bugs');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getPriorityIcon = (title) => {
    if (title.includes('🔴')) return '🔴';
    if (title.includes('🟡')) return '🟡';
    if (title.includes('🟢')) return '🟢';
    return '⚪';
  };

  const cleanTitle = (title) => {
    return title.replace(/^[🔴🟡🟢⚪]\s*/, '');
  };

  if (loading) {
    return (
      <div className="bug-list">
        <div className="loading">
          <div className="loading-spinner"></div>
          <p>Loading bugs...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bug-list">
        <div className="error-message">
          <h3>Error Loading Bugs</h3>
          <p>{error}</p>
          <button onClick={fetchBugs} className="retry-btn">
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="bug-list">
      <div className="bug-list-header">
        <h2>Submitted Bugs</h2>
        <button onClick={fetchBugs} className="refresh-btn">
          🔄 Refresh
        </button>
      </div>

      {bugs.length === 0 ? (
        <div className="empty-state">
          <div className="empty-icon">🐛</div>
          <h3>No bugs submitted yet</h3>
          <p>Submit your first bug report using the form above.</p>
        </div>
      ) : (
        <div className="bug-items">
          {bugs.map((bug) => (
            <div key={bug.id} className={`bug-item ${bug.completed ? 'completed' : ''}`}>
              <div className="bug-header">
                <div className="bug-priority">
                  {getPriorityIcon(bug.title)}
                </div>
                <div className="bug-title">
                  {cleanTitle(bug.title)}
                </div>
                <div className="bug-status">
                  {bug.completed ? '✅' : '⏳'}
                </div>
              </div>
              
              {bug.description && (
                <div className="bug-description">
                  {bug.description}
                </div>
              )}
              
              <div className="bug-footer">
                <div className="bug-date">
                  Created: {formatDate(bug.createdAt)}
                </div>
                {bug.basecampUrl && (
                  <a
                    href={bug.basecampUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="basecamp-link"
                  >
                    View in Basecamp →
                  </a>
                )}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default BugList;
