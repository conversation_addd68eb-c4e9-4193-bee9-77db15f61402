import axios from 'axios';
import fs from 'fs/promises';
import path from 'path';
import { config } from '../config/config.js';

class AuthService {
  constructor() {
    this.tokenFilePath = path.join(process.cwd(), '.tokens.json');
    this.tokens = {
      accessToken: null,
      refreshToken: null,
      expiresAt: null
    };
    this.loadTokens();
  }

  async loadTokens() {
    try {
      const tokenData = await fs.readFile(this.tokenFilePath, 'utf8');
      this.tokens = JSON.parse(tokenData);
    } catch (error) {
      console.log('No existing tokens found, will need to authenticate');
    }
  }

  async saveTokens() {
    try {
      await fs.writeFile(this.tokenFilePath, JSON.stringify(this.tokens, null, 2));
    } catch (error) {
      console.error('Error saving tokens:', error);
    }
  }

  getAuthUrl() {
    const params = new URLSearchParams({
      type: 'web_server',
      client_id: config.basecamp.clientId,
      redirect_uri: config.basecamp.redirectUri
    });
    
    return `${config.basecamp.authUrl}?${params.toString()}`;
  }

  async exchangeCodeForToken(code) {
    try {
      const params = new URLSearchParams({
        type: 'web_server',
        client_id: config.basecamp.clientId,
        client_secret: config.basecamp.clientSecret,
        redirect_uri: config.basecamp.redirectUri,
        code: code
      });

      const response = await axios.post(config.basecamp.tokenUrl, params, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      });

      const { access_token, refresh_token, expires_in } = response.data;
      
      this.tokens = {
        accessToken: access_token,
        refreshToken: refresh_token,
        expiresAt: Date.now() + (expires_in * 1000)
      };

      await this.saveTokens();
      return this.tokens;
    } catch (error) {
      console.error('Error exchanging code for token:', error.response?.data || error.message);
      throw error;
    }
  }

  async refreshAccessToken() {
    if (!this.tokens.refreshToken) {
      throw new Error('No refresh token available');
    }

    try {
      const params = new URLSearchParams({
        type: 'refresh',
        client_id: config.basecamp.clientId,
        client_secret: config.basecamp.clientSecret,
        refresh_token: this.tokens.refreshToken
      });

      const response = await axios.post(config.basecamp.tokenUrl, params, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      });

      const { access_token, expires_in } = response.data;
      
      this.tokens.accessToken = access_token;
      this.tokens.expiresAt = Date.now() + (expires_in * 1000);

      await this.saveTokens();
      return this.tokens.accessToken;
    } catch (error) {
      console.error('Error refreshing token:', error.response?.data || error.message);
      throw error;
    }
  }

  async getValidAccessToken() {
    // Check if token exists and is not expired
    if (this.tokens.accessToken && this.tokens.expiresAt > Date.now() + 60000) {
      return this.tokens.accessToken;
    }

    // Try to refresh if we have a refresh token
    if (this.tokens.refreshToken) {
      try {
        return await this.refreshAccessToken();
      } catch (error) {
        console.error('Failed to refresh token:', error);
      }
    }

    throw new Error('No valid access token available. Please re-authenticate.');
  }

  isAuthenticated() {
    return this.tokens.accessToken && this.tokens.expiresAt > Date.now();
  }
}

export default new AuthService();
