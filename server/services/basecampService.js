import axios from 'axios';
import { config } from '../config/config.js';
import authService from './authService.js';

class BasecampService {
  constructor() {
    this.baseUrl = `${config.basecamp.apiBaseUrl}/${config.basecamp.accountId}`;
    this.userAgent = 'Bug Tracker App (<EMAIL>)';
  }

  async makeRequest(method, endpoint, data = null) {
    try {
      const accessToken = await authService.getValidAccessToken();
      
      const requestConfig = {
        method,
        url: `${this.baseUrl}${endpoint}`,
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'User-Agent': this.userAgent,
          'Content-Type': 'application/json'
        }
      };

      if (data) {
        requestConfig.data = data;
      }

      const response = await axios(requestConfig);
      return response.data;
    } catch (error) {
      console.error('Basecamp API Error:', error.response?.data || error.message);
      throw error;
    }
  }

  async createTodo(bugData) {
    const {
      title,
      description,
      priority,
      category,
      stepsToReproduce,
      expectedBehavior,
      actualBehavior,
      environment,
      screenshot
    } = bugData;

    // Format the todo content
    const todoContent = this.formatBugDescription(title, priority, category);
    const todoNotes = this.formatBugNotes(bugData);

    const todoData = {
      content: todoContent,
      notes: todoNotes,
      assignee_ids: [], // Can be populated if needed
      completion_subscriber_ids: [],
      notify: false
    };

    try {
      const todo = await this.makeRequest(
        'POST',
        `/buckets/${config.basecamp.projectId}/todolists/${config.basecamp.todolistId}/todos.json`,
        todoData
      );

      // If there's a screenshot, upload it as an attachment
      if (screenshot) {
        await this.attachScreenshot(todo.id, screenshot);
      }

      return todo;
    } catch (error) {
      console.error('Error creating todo:', error);
      throw error;
    }
  }

  formatBugDescription(title, priority, category) {
    const priorityEmoji = {
      'Low': '🟢',
      'Medium': '🟡',
      'High': '🔴'
    };

    const categoryPrefix = category ? `[${category}] ` : '';
    return `${priorityEmoji[priority] || '⚪'} ${categoryPrefix}${title}`;
  }

  formatBugNotes(bugData) {
    const {
      description,
      stepsToReproduce,
      expectedBehavior,
      actualBehavior,
      environment
    } = bugData;

    let notes = '';

    if (description) {
      notes += `**Description:**\n${description}\n\n`;
    }

    if (stepsToReproduce) {
      notes += `**Steps to Reproduce:**\n${stepsToReproduce}\n\n`;
    }

    if (expectedBehavior || actualBehavior) {
      if (expectedBehavior) {
        notes += `**Expected Behavior:**\n${expectedBehavior}\n\n`;
      }
      if (actualBehavior) {
        notes += `**Actual Behavior:**\n${actualBehavior}\n\n`;
      }
    }

    if (environment) {
      notes += `**Environment:**\n${environment}\n\n`;
    }

    return notes.trim();
  }

  async attachScreenshot(todoId, screenshotData) {
    try {
      // First, upload the file to Basecamp
      const uploadData = {
        name: screenshotData.originalname || 'screenshot.png',
        content_type: screenshotData.mimetype || 'image/png',
        content: screenshotData.buffer.toString('base64')
      };

      const upload = await this.makeRequest('POST', '/uploads.json', uploadData);
      
      // Then attach it to the todo
      const attachmentData = {
        attachable_sgid: upload.attachable_sgid
      };

      await this.makeRequest(
        'POST',
        `/buckets/${config.basecamp.projectId}/recordings/${todoId}/attachments.json`,
        attachmentData
      );

      return upload;
    } catch (error) {
      console.error('Error attaching screenshot:', error);
      throw error;
    }
  }

  async getTodos() {
    try {
      const todos = await this.makeRequest(
        'GET',
        `/buckets/${config.basecamp.projectId}/todolists/${config.basecamp.todolistId}/todos.json`
      );
      return todos;
    } catch (error) {
      console.error('Error fetching todos:', error);
      throw error;
    }
  }

  async getProjects() {
    try {
      const projects = await this.makeRequest('GET', '/projects.json');
      return projects;
    } catch (error) {
      console.error('Error fetching projects:', error);
      throw error;
    }
  }

  async getTodoLists(projectId) {
    try {
      const todoLists = await this.makeRequest('GET', `/buckets/${projectId}/todosets.json`);
      return todoLists;
    } catch (error) {
      console.error('Error fetching todo lists:', error);
      throw error;
    }
  }
}

export default new BasecampService();
