import axios from 'axios';
import { config } from '../config/config.js';
import authService from './authService.js';

class BasecampService {
  constructor() {
    this.baseUrl = `${config.basecamp.apiBaseUrl}/${config.basecamp.accountId}`;
    this.userAgent = 'Bug Tracker App (<EMAIL>)';
  }

  async makeRequest(method, endpoint, data = null) {
    try {
      const accessToken = await authService.getValidAccessToken();
      
      const requestConfig = {
        method,
        url: `${this.baseUrl}${endpoint}`,
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'User-Agent': this.userAgent,
          'Content-Type': 'application/json'
        }
      };

      if (data) {
        requestConfig.data = data;
      }

      const response = await axios(requestConfig);
      return response.data;
    } catch (error) {
      console.error('Basecamp API Error:', error.response?.data || error.message);
      throw error;
    }
  }

  async createTodo(bugData) {
    console.log('🔍 createTodo received bugData:', JSON.stringify(bugData, null, 2));

    const {
      title,
      description,
      priority,
      category,
      stepsToReproduce,
      expectedBehavior,
      actualBehavior,
      environment,
      screenshot
    } = bugData;

    console.log('🔍 Destructured fields:');
    console.log('  title:', title);
    console.log('  description:', description);
    console.log('  priority:', priority);
    console.log('  category:', category);
    console.log('  stepsToReproduce:', stepsToReproduce);
    console.log('  expectedBehavior:', expectedBehavior);
    console.log('  actualBehavior:', actualBehavior);
    console.log('  environment:', environment);

    try {
      // Test: Try single API call with both content and description
      const todoContent = this.formatBugTitle(title, priority, category);
      console.log('📝 Formatted title:', todoContent);

      const todoDescription = this.formatBugNotes(bugData);
      console.log('📄 Formatted description:', todoDescription);

      const singleCallPayload = {
        content: todoContent,
        description: todoDescription,
        assignee_ids: [],
        completion_subscriber_ids: [],
        notify: false
      };

      console.log('🚀 Final payload being sent to Basecamp:', JSON.stringify(singleCallPayload, null, 2));

      try {
        const todo = await this.makeRequest(
          'POST',
          `/buckets/${config.basecamp.projectId}/todolists/${config.basecamp.todolistId}/todos.json`,
          singleCallPayload
        );

        console.log('✅ Single API call SUCCESS! Todo created with ID:', todo.id);

        // If there's a screenshot, upload it as an attachment
        if (screenshot) {
          await this.attachScreenshot(todo.id, screenshot);
        }

        return todo;
      } catch (singleCallError) {
        console.log('❌ Single API call failed, falling back to two-step process');
        console.log('Single call error:', singleCallError.message);

        // Fallback: Two-step process
        const createPayload = {
          content: todoContent,
          assignee_ids: [],
          completion_subscriber_ids: [],
          notify: false
        };

        console.log('Step 1 - Creating todo with payload:', JSON.stringify(createPayload, null, 2));

        const todo = await this.makeRequest(
          'POST',
          `/buckets/${config.basecamp.projectId}/todolists/${config.basecamp.todolistId}/todos.json`,
          createPayload
        );

        console.log('Todo created successfully with ID:', todo.id);

        // Step 2: Update the todo with description
        const updatePayload = {
          description: todoDescription
        };

        console.log('Step 2 - Updating todo with description:', JSON.stringify(updatePayload, null, 2));

        await this.makeRequest(
          'PATCH',
          `/buckets/${config.basecamp.projectId}/todos/${todo.id}.json`,
          updatePayload
        );

        console.log('Todo description updated successfully');

        // If there's a screenshot, upload it as an attachment
        if (screenshot) {
          await this.attachScreenshot(todo.id, screenshot);
        }

        return todo;
      }
    } catch (error) {
      console.error('Error creating todo:', error);
      throw error;
    }
  }

  formatBugTitle(title, priority, category) {
    const categoryPrefix = category ? `[${category}] ` : '';
    return `Bug: ${categoryPrefix}${title}`;
  }



  formatBugNotes(bugData) {
    const {
      priority,
      category,
      description,
      stepsToReproduce,
      expectedBehavior,
      actualBehavior,
      environment
    } = bugData;

    console.log('📋 formatBugNotes received data:', JSON.stringify(bugData, null, 2));
    console.log('📋 Field analysis:');
    console.log('  priority:', priority, '(truthy:', !!priority, ')');
    console.log('  category:', category, '(truthy:', !!category, ')');
    console.log('  description:', description, '(truthy:', !!description, ')');
    console.log('  stepsToReproduce:', stepsToReproduce, '(truthy:', !!stepsToReproduce, ')');
    console.log('  expectedBehavior:', expectedBehavior, '(truthy:', !!expectedBehavior, ')');
    console.log('  actualBehavior:', actualBehavior, '(truthy:', !!actualBehavior, ')');
    console.log('  environment:', environment, '(truthy:', !!environment, ')');

    // Use Basecamp's rich text format
    let notes = '';

    // Priority
    if (priority) {
      console.log('✅ Adding priority to notes');
      notes += `<strong>Priority:</strong> ${priority}<br>`;
    } else {
      console.log('❌ Skipping priority (empty)');
    }

    // Category
    if (category) {
      console.log('✅ Adding category to notes');
      notes += `<strong>Category:</strong> ${category}<br>`;
    } else {
      console.log('❌ Skipping category (empty)');
    }

    // Environment
    if (environment) {
      console.log('✅ Adding environment to notes');
      notes += `<strong>Environment:</strong> ${environment}<br>`;
    } else {
      console.log('❌ Skipping environment (empty)');
    }

    // Add separator line if we have metadata
    if (priority || category || environment) {
      notes += '<br>';
    }

    // Description
    if (description) {
      console.log('✅ Adding description to notes');
      notes += `<strong>Description:</strong><br>${description.replace(/\n/g, '<br>')}<br><br>`;
    } else {
      console.log('❌ Skipping description (empty)');
    }

    // Steps to reproduce
    if (stepsToReproduce) {
      console.log('✅ Adding steps to reproduce to notes');
      notes += `<strong>Steps to Reproduce:</strong><br>${this.formatSteps(stepsToReproduce)}<br><br>`;
    } else {
      console.log('❌ Skipping steps to reproduce (empty)');
    }

    // Expected Behavior
    if (expectedBehavior) {
      console.log('✅ Adding expected behavior to notes');
      notes += `<strong>Expected Behavior:</strong><br>${expectedBehavior.replace(/\n/g, '<br>')}<br><br>`;
    } else {
      console.log('❌ Skipping expected behavior (empty)');
    }

    // Actual Behavior
    if (actualBehavior) {
      console.log('✅ Adding actual behavior to notes');
      notes += `<strong>Actual Behavior:</strong><br>${actualBehavior.replace(/\n/g, '<br>')}<br><br>`;
    } else {
      console.log('❌ Skipping actual behavior (empty)');
    }

    console.log('📄 Final formatted notes:', notes);
    return notes;
  }

  formatSteps(stepsText) {
    if (!stepsText) return '';

    // Check if it's already formatted as a list
    const lines = stepsText.split('\n').filter(line => line.trim());

    // If lines start with numbers, convert to ordered list
    if (lines.some(line => /^\d+\./.test(line.trim()))) {
      const listItems = lines.map(line => {
        const cleaned = line.replace(/^\d+\.\s*/, '').trim();
        return `<li>${cleaned}</li>`;
      }).join('');
      return `<ol>${listItems}</ol>`;
    }

    // If lines start with bullets, convert to unordered list
    if (lines.some(line => /^[-*]\s/.test(line.trim()))) {
      const listItems = lines.map(line => {
        const cleaned = line.replace(/^[-*]\s*/, '').trim();
        return `<li>${cleaned}</li>`;
      }).join('');
      return `<ul>${listItems}</ul>`;
    }

    // Otherwise, just replace line breaks
    return stepsText.replace(/\n/g, '<br>');
  }



  async attachScreenshot(todoId, screenshotData) {
    try {
      // First, upload the file to Basecamp
      const uploadData = {
        name: screenshotData.originalname || 'screenshot.png',
        content_type: screenshotData.mimetype || 'image/png',
        content: screenshotData.buffer.toString('base64')
      };

      const upload = await this.makeRequest('POST', '/uploads.json', uploadData);
      
      // Then attach it to the todo
      const attachmentData = {
        attachable_sgid: upload.attachable_sgid
      };

      await this.makeRequest(
        'POST',
        `/buckets/${config.basecamp.projectId}/recordings/${todoId}/attachments.json`,
        attachmentData
      );

      return upload;
    } catch (error) {
      console.error('Error attaching screenshot:', error);
      throw error;
    }
  }

  async getTodos() {
    try {
      const todos = await this.makeRequest(
        'GET',
        `/buckets/${config.basecamp.projectId}/todolists/${config.basecamp.todolistId}/todos.json`
      );
      return todos;
    } catch (error) {
      console.error('Error fetching todos:', error);
      throw error;
    }
  }

  async getProjects() {
    try {
      const projects = await this.makeRequest('GET', '/projects.json');
      return projects;
    } catch (error) {
      console.error('Error fetching projects:', error);
      throw error;
    }
  }

  async getTodoLists(projectId) {
    try {
      const todoLists = await this.makeRequest('GET', `/buckets/${projectId}/todosets.json`);
      return todoLists;
    } catch (error) {
      console.error('Error fetching todo lists:', error);
      throw error;
    }
  }
}

export default new BasecampService();
