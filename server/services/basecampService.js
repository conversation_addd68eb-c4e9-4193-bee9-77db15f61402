import axios from 'axios';
import { config } from '../config/config.js';
import authService from './authService.js';

class BasecampService {
  constructor() {
    this.baseUrl = `${config.basecamp.apiBaseUrl}/${config.basecamp.accountId}`;
    this.userAgent = 'Bug Tracker App (<EMAIL>)';
  }

  async makeRequest(method, endpoint, data = null) {
    try {
      const accessToken = await authService.getValidAccessToken();
      
      const requestConfig = {
        method,
        url: `${this.baseUrl}${endpoint}`,
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'User-Agent': this.userAgent,
          'Content-Type': 'application/json'
        }
      };

      if (data) {
        requestConfig.data = data;
      }

      const response = await axios(requestConfig);
      return response.data;
    } catch (error) {
      console.error('Basecamp API Error:', error.response?.data || error.message);
      throw error;
    }
  }

  async createTodo(bugData) {
    const {
      title,
      description,
      priority,
      category,
      stepsToReproduce,
      expectedBehavior,
      actualBehavior,
      environment,
      screenshot
    } = bugData;

    try {
      // Step 1: Create the todo with just the title
      const todoContent = this.formatBugDescription(title, priority, category);
      const dueDate = new Date();
      dueDate.setDate(dueDate.getDate() + 7); // Due in 7 days

      const todoData = {
        content: todoContent,
        assignee_ids: [],
        completion_subscriber_ids: [],
        due_on: dueDate.toISOString().split('T')[0] // Format as YYYY-MM-DD
      };

      const todo = await this.makeRequest(
        'POST',
        `/buckets/${config.basecamp.projectId}/todolists/${config.basecamp.todolistId}/todos.json`,
        todoData
      );

      // Step 2: Update the todo with the formatted description
      const formattedDescription = this.formatBugDescription2(bugData);

      const updateData = {
        description: formattedDescription
      };

      await this.makeRequest(
        'PUT',
        `/buckets/${config.basecamp.projectId}/recordings/${todo.id}.json`,
        updateData
      );

      // Step 3: If there's a screenshot, upload it as an attachment
      if (screenshot) {
        await this.attachScreenshot(todo.id, screenshot);
      }

      return todo;
    } catch (error) {
      console.error('Error creating todo:', error);
      throw error;
    }
  }

  formatBugDescription(title, priority, category) {
    const priorityEmoji = {
      'Low': '🟢',
      'Medium': '🟡',
      'High': '🔴'
    };

    const categoryPrefix = category ? `[${category}] ` : '';
    return `🚨 Bug: ${categoryPrefix}${title}`;
  }

  formatBugDescription2(bugData) {
    const {
      priority,
      category,
      description,
      stepsToReproduce,
      expectedBehavior,
      actualBehavior,
      environment
    } = bugData;

    let formattedDesc = '<div>';

    // Priority
    if (priority) {
      formattedDesc += `<strong>Priority:</strong> ${priority}<br>`;
    }

    // Category
    if (category) {
      formattedDesc += `<strong>Category:</strong> ${category}<br>`;
    }

    // Environment
    if (environment) {
      formattedDesc += `<strong>Device:</strong> ${environment}<br>`;
    }

    // Description
    if (description) {
      formattedDesc += `<strong>Description:</strong><br>${this.convertMarkdownToHtml(description)}<br>`;
    }

    // Steps to reproduce
    if (stepsToReproduce) {
      formattedDesc += `<strong>Steps:</strong><br>${this.convertMarkdownToHtml(stepsToReproduce)}<br>`;
    }

    // Expected vs Actual
    if (expectedBehavior) {
      formattedDesc += `<strong>Expected:</strong> ${this.convertMarkdownToHtml(expectedBehavior)}<br>`;
    }

    if (actualBehavior) {
      formattedDesc += `<strong>Actual:</strong> ${this.convertMarkdownToHtml(actualBehavior)}<br>`;
    }

    formattedDesc += '</div>';

    return formattedDesc;
  }

  formatBugNotes(bugData) {
    const {
      description,
      stepsToReproduce,
      expectedBehavior,
      actualBehavior,
      environment
    } = bugData;

    let notes = '';

    if (description) {
      notes += `<h3>📝 Description</h3>\n${this.convertMarkdownToHtml(description)}\n\n`;
    }

    if (stepsToReproduce) {
      notes += `<h3>🔄 Steps to Reproduce</h3>\n${this.convertMarkdownToHtml(stepsToReproduce)}\n\n`;
    }

    if (expectedBehavior || actualBehavior) {
      notes += `<h3>🎯 Expected vs Actual Behavior</h3>\n`;
      if (expectedBehavior) {
        notes += `<h4>✅ Expected:</h4>\n${this.convertMarkdownToHtml(expectedBehavior)}\n\n`;
      }
      if (actualBehavior) {
        notes += `<h4>❌ Actual:</h4>\n${this.convertMarkdownToHtml(actualBehavior)}\n\n`;
      }
    }

    if (environment) {
      notes += `<h3>💻 Environment</h3>\n<p>${environment}</p>\n\n`;
    }

    // Add metadata footer
    notes += `<hr>\n<p><small>🐛 Submitted via Bug Tracker on ${new Date().toLocaleString()}</small></p>`;

    return notes.trim();
  }

  convertMarkdownToHtml(markdown) {
    if (!markdown) return '';

    let html = markdown;

    // Bold
    html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

    // Italic
    html = html.replace(/\*(.*?)\*/g, '<em>$1</em>');

    // Strikethrough (underline in our case)
    html = html.replace(/~~(.*?)~~/g, '<u>$1</u>');

    // Ordered lists
    html = html.replace(/^\d+\.\s(.*)$/gm, '<li>$1</li>');

    // Unordered lists
    html = html.replace(/^[-*]\s(.*)$/gm, '<li>$1</li>');

    // Wrap consecutive list items
    html = html.replace(/(<li>.*<\/li>)/gs, (match) => {
      if (match.includes('1.') || /^\d+\./.test(markdown)) {
        return `<ol>${match}</ol>`;
      } else {
        return `<ul>${match}</ul>`;
      }
    });

    // Line breaks
    html = html.replace(/\n/g, '<br>');

    return html;
  }

  async attachScreenshot(todoId, screenshotData) {
    try {
      // First, upload the file to Basecamp
      const uploadData = {
        name: screenshotData.originalname || 'screenshot.png',
        content_type: screenshotData.mimetype || 'image/png',
        content: screenshotData.buffer.toString('base64')
      };

      const upload = await this.makeRequest('POST', '/uploads.json', uploadData);
      
      // Then attach it to the todo
      const attachmentData = {
        attachable_sgid: upload.attachable_sgid
      };

      await this.makeRequest(
        'POST',
        `/buckets/${config.basecamp.projectId}/recordings/${todoId}/attachments.json`,
        attachmentData
      );

      return upload;
    } catch (error) {
      console.error('Error attaching screenshot:', error);
      throw error;
    }
  }

  async getTodos() {
    try {
      const todos = await this.makeRequest(
        'GET',
        `/buckets/${config.basecamp.projectId}/todolists/${config.basecamp.todolistId}/todos.json`
      );
      return todos;
    } catch (error) {
      console.error('Error fetching todos:', error);
      throw error;
    }
  }

  async getProjects() {
    try {
      const projects = await this.makeRequest('GET', '/projects.json');
      return projects;
    } catch (error) {
      console.error('Error fetching projects:', error);
      throw error;
    }
  }

  async getTodoLists(projectId) {
    try {
      const todoLists = await this.makeRequest('GET', `/buckets/${projectId}/todosets.json`);
      return todoLists;
    } catch (error) {
      console.error('Error fetching todo lists:', error);
      throw error;
    }
  }
}

export default new BasecampService();
