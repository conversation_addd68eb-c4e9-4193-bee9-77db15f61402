{"name": "bug-tracker-server", "version": "1.0.0", "description": "Backend server for bug tracker with Basecamp integration", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"axios": "^1.6.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "form-data": "^4.0.3", "helmet": "^7.1.0", "multer": "^1.4.5-lts.1"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["bug-tracker", "basecamp", "api"], "author": "<PERSON>", "license": "MIT"}