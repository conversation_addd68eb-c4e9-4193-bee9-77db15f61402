import express from 'express';
import multer from 'multer';
import basecampService from '../services/basecampService.js';
import { requireAuth } from '../middleware/auth.js';

const router = express.Router();

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    // Accept images only
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'), false);
    }
  }
});

// Submit a new bug
router.post('/submit', upload.single('screenshot'), async (req, res) => {
  try {
    const {
      title,
      description,
      priority,
      category,
      stepsToReproduce,
      expectedBehavior,
      actualBehavior,
      environment
    } = req.body;

    // Validate required fields
    if (!title || !priority || !description) {
      return res.status(400).json({
        error: 'Title, description, and priority are required'
      });
    }

    // Validate priority value
    const validPriorities = ['Low', 'Medium', 'High'];
    if (!validPriorities.includes(priority)) {
      return res.status(400).json({
        error: 'Priority must be one of: Low, Medium, High'
      });
    }

    const bugData = {
      title: title.trim(),
      description: description?.trim() || '',
      priority,
      category: category?.trim() || '',
      stepsToReproduce: stepsToReproduce?.trim() || '',
      expectedBehavior: expectedBehavior?.trim() || '',
      actualBehavior: actualBehavior?.trim() || '',
      environment: environment?.trim() || '',
      screenshot: req.file || null
    };

    console.log('Received bug data from form:', JSON.stringify(bugData, null, 2));

    // Create todo in Basecamp
    const todo = await basecampService.createTodo(bugData);

    res.status(201).json({
      success: true,
      message: 'Bug submitted successfully',
      bug: {
        id: todo.id,
        title: bugData.title,
        description: bugData.description,
        priority: bugData.priority,
        category: bugData.category,
        basecampUrl: todo.app_url,
        createdAt: todo.created_at
      }
    });

  } catch (error) {
    console.error('Error submitting bug:', error);
    
    if (error.response?.status === 401) {
      return res.status(401).json({
        error: 'Authentication with Basecamp failed',
        message: 'Please check Basecamp credentials and try again'
      });
    }

    res.status(500).json({
      error: 'Failed to submit bug',
      message: error.message
    });
  }
});

// Get submitted bugs (optional feature)
router.get('/list', async (req, res) => {
  try {
    const todos = await basecampService.getTodos();
    
    const bugs = todos.map(todo => ({
      id: todo.id,
      title: todo.content,
      description: todo.notes,
      completed: todo.completed,
      basecampUrl: todo.app_url,
      createdAt: todo.created_at,
      updatedAt: todo.updated_at
    }));

    res.json({
      success: true,
      bugs
    });

  } catch (error) {
    console.error('Error fetching bugs:', error);
    
    if (error.response?.status === 401) {
      return res.status(401).json({
        error: 'Authentication with Basecamp failed'
      });
    }

    res.status(500).json({
      error: 'Failed to fetch bugs',
      message: error.message
    });
  }
});

// Get Basecamp projects (for setup/debugging)
router.get('/projects', async (req, res) => {
  try {
    const projects = await basecampService.getProjects();
    res.json({ success: true, projects });
  } catch (error) {
    console.error('Error fetching projects:', error);
    res.status(500).json({
      error: 'Failed to fetch projects',
      message: error.message
    });
  }
});

// Get todo lists for a project (for setup/debugging)
router.get('/projects/:projectId/todolists', async (req, res) => {
  try {
    const { projectId } = req.params;
    const todoLists = await basecampService.getTodoLists(projectId);
    res.json({ success: true, todoLists });
  } catch (error) {
    console.error('Error fetching todo lists:', error);
    res.status(500).json({
      error: 'Failed to fetch todo lists',
      message: error.message
    });
  }
});

export default router;
