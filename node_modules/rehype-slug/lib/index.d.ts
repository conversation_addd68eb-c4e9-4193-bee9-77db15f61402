/**
 * Add `id`s to headings.
 *
 * @param {Options | null | undefined} [options]
 *   Configuration (optional).
 * @returns
 *   Transform.
 */
export default function rehypeSlug(options?: Options | null | undefined): (tree: Root) => undefined;
export type Root = import('hast').Root;
/**
 * Configuration (optional).
 */
export type Options = {
    /**
     * Prefix to add in front of `id`s (default: `''`).
     */
    prefix?: string;
};
