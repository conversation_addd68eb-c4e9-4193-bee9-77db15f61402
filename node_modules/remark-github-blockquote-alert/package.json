{"name": "remark-github-blockquote-alert", "version": "1.3.1", "description": "Alerts are a Markdown extension based on the blockquote syntax that you can use to emphasize critical information.", "homepage": "https://jaywcjlove.github.io/remark-github-blockquote-alert", "funding": "https://jaywcjlove.github.io/#/sponsor", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "sideEffects": false, "type": "module", "typings": "./lib/index.d.ts", "exports": {".": "./lib/index.js", "./alert.css": "./alert.css"}, "scripts": {"prepack": "npm run build", "start": "node lib/index.js", "watch": "tsbb watch", "build": "tsbb build", "type-check": "tsc --noEmit", "test": "tsbb test", "coverage": "tsbb test --coverage"}, "repository": {"type": "git", "url": "https://github.com/jaywcjlove/remark-github-blockquote-alert"}, "files": ["alert.css", "src", "lib"], "keywords": ["remark", "remark-plugin", "remark-github-blockquote-alert", "alert", "element", "markdown", "javascript", "html", "ast", "unified"], "jest": {"extensionsToTreatAsEsm": [".ts"], "moduleNameMapper": {"^(\\.{1,2}/.*)\\.js$": "$1"}, "transformIgnorePatterns": ["<rootDir>/node_modules/?!(.*)"]}, "engines": {"node": ">=16"}, "dependencies": {"unist-util-visit": "^5.0.0"}, "devDependencies": {"hast-util-select": "^6.0.0", "rehype": "^13.0.0", "rehype-raw": "^7.0.0", "rehype-stringify": "^10.0.0", "remark": "^15.0.1", "remark-parse": "^11.0.0", "remark-rehype": "^11.0.0", "tsbb": "^4.2.5", "unified": "^11.0.4"}}