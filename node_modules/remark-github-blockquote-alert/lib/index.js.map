{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,KAAK,EAAE,MAAM,kBAAkB,CAAC;AAIzC,MAAM,UAAU,GAAG,6CAA6C,CAAC;AACjE,MAAM,gBAAgB,GAAG,oDAAoD,CAAC;AAgB9E;;;;GAIG;AACH,MAAM,CAAC,MAAM,WAAW,GAA4B,CAAC,EAAE,WAAW,GAAG,KAAK,EAAE,OAAO,GAAG,KAAK,EAAE,GAAG,EAAE,EAAE,EAAE;IACpG,OAAO,CAAC,IAAI,EAAE,EAAE;QACd,KAAK,CAAC,IAAI,EAAE,YAAY,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE;YAChD,IAAI,SAAS,GAAG,EAAE,CAAC;YACnB,IAAI,KAAK,GAAG,EAAE,CAAC;YACf,IAAI,MAAM,GAAG,IAAI,CAAC;YAClB,IAAI,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;gBACrC,IAAI,MAAM,IAAI,IAAI,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;oBACxC,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;oBACnC,MAAM,IAAI,GAAG,SAAS,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;oBAC9D,MAAM,GAAG,GAAG,WAAW,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,UAAU,CAAC;oBACxD,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;oBAC9B,IAAI,KAAK,EAAE,CAAC;wBACV,MAAM,GAAG,KAAK,CAAC;wBACf,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;wBACzC,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,SAAS,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,iBAAiB,EAAE,CAAC;wBAChG,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;4BACxB,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG;gCACjB,IAAI,EAAE,MAAM;gCACZ,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;6BACjD,CAAC;wBACJ,CAAC;wBAED,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;4BACzB,MAAM,SAAS,GAA2B,EAAE,CAAC;4BAC7C,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,GAAG,EAAE,EAAE;gCAClC,IAAI,GAAG,IAAI,CAAC;oCAAE,OAAO;gCACrB,IAAI,GAAG,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;oCACtC,OAAO;gCACT,CAAC;gCACD,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;4BACvB,CAAC,CAAC,CAAC;4BACH,IAAI,CAAC,QAAQ,GAAG,CAAC,GAAG,SAAS,CAAC,CAAC;wBACjC,CAAC;oBACH,CAAC;gBACH,CAAC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC,CAAC,CAAA;YAEF,IAAI,CAAC,CAAC,SAAS,EAAE,CAAC;gBAChB,IAAI,CAAC,IAAI,GAAG;oBACV,KAAK,EAAE,OAAO;oBACd,WAAW,EAAE;wBACX,KAAK,EAAE,iCAAiC,SAAS,EAAE;wBACnD,GAAG,EAAE,MAAM;qBACZ;iBACF,CAAA;gBACD,KAAK,CAAC,OAAO,CAAC;oBACZ,IAAI,EAAE,WAAW;oBACjB,QAAQ,EAAE;wBACR,YAAY,CAAC,SAAqB,CAAC;wBACnC;4BACE,IAAI,EAAE,MAAM;4BACZ,KAAK,EAAE,KAAK,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;yBAChC;qBACF;oBACD,IAAI,EAAE;wBACJ,WAAW,EAAE;4BACX,KAAK,EAAE,sBAAsB;4BAC7B,GAAG,EAAE,MAAM;yBACZ;qBACF;iBACF,CAAC,CAAA;YACJ,CAAC;YACD,IAAI,CAAC,QAAQ,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;AACJ,CAAC,CAAC;AAEF,eAAe,WAAW,CAAC;AAE3B,MAAM,UAAU,YAAY,CAAC,IAAc;IACzC,IAAI,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;IACjC,OAAO;QACL,IAAI,EAAE,UAAU;QAChB,IAAI,EAAE;YACJ,KAAK,EAAE,KAAK;YACZ,WAAW,EAAE;gBACX,KAAK,EAAE,SAAS;gBAChB,OAAO,EAAE,WAAW;gBACpB,KAAK,EAAE,IAAI;gBACX,MAAM,EAAE,IAAI;gBACZ,UAAU,EAAE,MAAM;aACnB;SACF;QACD,QAAQ,EAAE;YACR;gBACE,IAAI,EAAE,UAAU;gBAChB,IAAI,EAAE;oBACJ,KAAK,EAAE,MAAM;oBACb,WAAW,EAAE;wBACX,CAAC,EAAE,KAAK;qBACT;iBACF;gBACD,QAAQ,EAAE,EAAE;aACb;SACF;KACF,CAAA;AACH,CAAC;AAID,MAAM,QAAQ,GAA6B;IACzC,IAAI,EAAE,yPAAyP;IAC/P,GAAG,EAAE,urBAAurB;IAC5rB,SAAS,EACP,maAAma;IACra,OAAO,EACL,gTAAgT;IAClT,OAAO,EACL,oYAAoY;CACvY,CAAC"}