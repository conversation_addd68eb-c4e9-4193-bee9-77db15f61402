export const getURLParameters = (url = '') => (url.match(/([^?=&]+)(=([^&]*))/g) || []).reduce((a, v) => ((a[v.slice(0, v.indexOf('='))] = v.slice(v.indexOf('=') + 1)), a), {});
export const prevChild = (data = [], index) => {
    let i = index;
    while (i > -1) {
        i--;
        if (!data[i])
            return;
        if ((data[i] && data[i].value && data[i].value.replace(/(\n|\s)/g, '') !== '') || data[i].type !== 'text') {
            if (!/^rehype:/.test(data[i].value) || data[i].type !== 'comment')
                return;
            return data[i];
        }
    }
    return;
};
export const nextChild = (data = [], index, tagName, codeBlockParames) => {
    let i = index;
    while (i < data.length) {
        i++;
        if (tagName) {
            const element = data[i];
            if (element && element.value && element.value.replace(/(\n|\s)/g, '') !== '' || data[i] && data[i].type === 'element') {
                return element.tagName === tagName ? element : undefined;
            }
        }
        else {
            const element = data[i];
            if (!element || element.type === 'element')
                return;
            if (element.type === 'text' && element.value.replace(/(\n|\s)/g, '') !== '')
                return;
            if (element.type && /^(comment|raw)$/ig.test(element.type)) {
                if (element.value && !/^rehype:/.test(element.value.replace(/^(\s+)?<!--(.*?)-->/, '$2') || '')) {
                    return;
                }
                ;
                if (codeBlockParames) {
                    const nextNode = nextChild(data, i, 'pre', codeBlockParames);
                    if (nextNode)
                        return;
                    element.value = (element.value || '').replace(/^(\n|\s)+/, '');
                    return element;
                }
                else {
                    element.value = (element.value || '').replace(/^(\n|\s)+/, '');
                    return element;
                }
            }
        }
    }
    return;
};
/**
 * 获取代码注视的位置
 * @param data 数据
 * @param index 当前数据所在的位置
 * @returns 返回 当前参数数据 Object，`{}`
 */
export const getCommentObject = ({ value = '' }) => {
    const param = getURLParameters(value.replace(/^<!--(.*?)-->/, '$1').replace(/^rehype:/, ''));
    Object.keys(param).forEach((keyName) => {
        if (param[keyName] === 'true') {
            param[keyName] = true;
        }
        if (param[keyName] === 'false') {
            param[keyName] = false;
        }
        if (typeof param[keyName] === 'string' && !/^0/.test(param[keyName]) && !isNaN(+param[keyName])) {
            param[keyName] = +param[keyName];
        }
    });
    return param;
};
export const propertiesHandle = (defaultAttrs, attrs, type) => {
    if (type === 'string') {
        return { ...defaultAttrs, 'data-config': JSON.stringify({ ...attrs, rehyp: true }) };
    }
    else if (type === 'attr') {
        return { ...defaultAttrs, ...attrs };
    }
    return { ...defaultAttrs, 'data-config': { ...attrs, rehyp: true } };
};
//# sourceMappingURL=utils.js.map