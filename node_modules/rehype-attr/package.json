{"name": "rehype-attr", "version": "3.0.3", "description": "New syntax to add attributes to Mark<PERSON>.", "homepage": "https://jaywcjlove.github.io/rehype-attr", "funding": "https://jaywcjlove.github.io/#/sponsor", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "sideEffects": false, "type": "module", "typings": "./lib/index.d.ts", "exports": "./lib/index.js", "scripts": {"prepack": "npm run build", "start": "node lib/index.js", "watch": "tsbb watch", "build": "tsbb build", "type-check": "tsc --noEmit", "test": "tsbb test", "coverage": "tsbb test --coverage"}, "repository": {"type": "git", "url": "https://github.com/jaywcjlove/rehype-attr"}, "files": ["src", "lib"], "keywords": ["attributes", "rehype", "markdown", "rehype-attr", "javascript", "html", "ast", "unified"], "jest": {"extensionsToTreatAsEsm": [".ts"], "moduleNameMapper": {"^(\\.{1,2}/.*)\\.js$": "$1"}, "transformIgnorePatterns": ["<rootDir>/node_modules/?!(.*)"]}, "engines": {"node": ">=16"}, "dependencies": {"unified": "~11.0.0", "unist-util-visit": "~5.0.0"}, "devDependencies": {"rehype": "~13.0.0", "rehype-raw": "~7.0.0", "rehype-stringify": "~10.0.0", "remark-gfm": "~4.0.0", "remark-parse": "~11.0.0", "remark-rehype": "~11.0.0", "tsbb": "^4.2.3"}}