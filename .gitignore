# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Dependencies
node_modules
server/node_modules

# Build outputs
dist
dist-ssr
build

# Environment variables (IMPORTANT: Contains sensitive credentials)
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
server/.env

# Token storage (IMPORTANT: Contains OAuth tokens)
server/.tokens.json
.tokens.json

# Local development
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Temporary files
tmp/
temp/
